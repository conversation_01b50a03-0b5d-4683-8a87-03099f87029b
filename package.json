{"name": "nextn", "version": "0.1.1", "private": true, "scripts": {"dev": "next dev -p 9002", "build": "next build", "start": "node server.js", "test": "jest --coverage --watchAll=false --passWithNoTests", "test:watch": "jest --watch", "test:ci": "jest --watchAll=false --ci --passWithNoTests", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "ci": "npm run type-check && npm run lint && npm run test:ci && npm run build", "docker:build": "docker build -t seminar-hall-frontend .", "docker:run": "docker run -p 9002:9002 seminar-hall-frontend", "staging:up": "docker-compose -f docker-compose.staging.yml up -d", "staging:down": "docker-compose -f docker-compose.staging.yml down", "staging:logs": "docker-compose -f docker-compose.staging.yml logs -f", "prod:up": "docker-compose -f docker-compose.prod.yml up -d", "prod:down": "docker-compose -f docker-compose.prod.yml down", "health-check": "curl -f http://localhost:9002/health || exit 1"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack-query-firebase/react": "^1.0.5", "@tanstack/react-query": "^5.66.0", "@types/axios": "^0.9.36", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "firebase": "^11.8.1", "geist": "^1.3.1", "lucide-react": "^0.475.0", "mongodb": "^6.16.0", "next": "15.2.3", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.28.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.2.5", "tailwindcss": "^3.4.1", "typescript": "^5", "typescript-eslint": "^8.33.0"}}